import type { NextConfig } from 'next';
import type { RemotePattern } from 'next/dist/shared/lib/image-config';

import withA<PERSON>yzer from '@next/bundle-analyzer';

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: getRemotePatterns(),
  },
  experimental: {
    serverComponentsExternalPackages: [],
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/auth/sign-in',
        permanent: false,
      },
      {
        source: '/auth',
        destination: '/auth/sign-in',
        permanent: false,
      },
    ];
  },
};

function getRemotePatterns() {
  const remotePatterns: RemotePattern[] = [];

  remotePatterns.push({
    protocol: 'https',
    hostname: 'lh3.googleusercontent.com',
  });

  // Example placeholder images
  // remotePatterns.push({
  //   protocol: 'https',
  //   hostname: 'loremflickr.com',
  // });

  return remotePatterns;
}

export default withAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})(nextConfig);
