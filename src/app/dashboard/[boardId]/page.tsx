import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { routes } from '@/configuration';

interface BoardPageProps {
  params: Promise<{ boardId: string }>;
}

export default async function BoardPage({ params }: BoardPageProps) {
  const { boardId } = await params;

  const boardName = 'Board Name';

  return (
    <div>
      <DashboardHeader
        breadcrumbs={[
          { label: 'Dashboard', href: routes.dashboard.Index },
          { label: boardName, isCurrentPage: true },
        ]}
      />
    </div>
  );
}
