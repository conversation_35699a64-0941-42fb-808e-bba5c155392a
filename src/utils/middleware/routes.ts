import { type NextRequest, NextResponse, URLPattern } from 'next/server';

import { routes } from '@/configuration';
import { getSupabaseMiddlewareClient } from '@/lib/supabase/config/middleware';

export function findMatchingRouteHandler(url: string) {
  const routes = getProtectedRoutes();
  const path = url.split('?')[0];

  for (const route of routes) {
    const match = route.pattern.exec(path);
    if (match?.pathname) {
      return route.handler;
    }
  }
}

function getProtectedRoutes() {
  return [
    {
      pattern: new URLPattern({ pathname: '/reset-password' }),
      handler: async (request: NextRequest, response: NextResponse) => {
        const supabase = await getSupabaseMiddlewareClient(request, response);
        const { data: userData, error } = await supabase.auth.getUser();

        const hasSession = !!userData?.user?.id;
        if (error || !hasSession) {
          return NextResponse.redirect(
            new URL(routes.Index, request.nextUrl.origin).href
          );
        }

        return response;
      },
    },
    {
      pattern: new URLPattern({ pathname: '/auth*' }),
      handler: async (request: NextRequest, response: NextResponse) => {
        const supabase = await getSupabaseMiddlewareClient(request, response);
        const { data: userData } = await supabase.auth.getUser();

        const hasSession = !!userData?.user?.id;

        if (hasSession) {
          return NextResponse.redirect(
            new URL(routes.Index, request.nextUrl.origin).href
          );
        }

        return response;
      },
    },
    {
      pattern: new URLPattern({ pathname: '/dashboard*' }),
      handler: async (request: NextRequest, response: NextResponse) => {
        const supabase = await getSupabaseMiddlewareClient(request, response);
        const { data: userData, error } = await supabase.auth.getUser();

        const hasSession = !!userData?.user?.id;
        if (error || !hasSession) {
          return NextResponse.redirect(
            new URL(routes.Index, request.nextUrl.origin).href
          );
        }

        return response;
      },
    },
  ];
}

// async function checkAuthAndRedirect(
//   request: NextRequest,
//   response: NextResponse
// ) {
//   const { data: session } = await getSession();

//   const isAuthenticated = !!session?.user?.id;

//   if (!isAuthenticated) {
//     const redirectUrl = new URL(routes.Index, request.nextUrl.origin)
//       .href;
//     return NextResponse.redirect(redirectUrl);
//   }

//   return response;
// }
