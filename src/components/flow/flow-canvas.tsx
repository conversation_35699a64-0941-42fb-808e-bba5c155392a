'use client';

import { useCallback, useState } from 'react';
import {
  <PERSON>actFlow,
  Background,
  Controls,
  MiniMap,
  addEdge,
  useNodesState,
  useEdgesState,
  type Connection,
  type Edge,
  type Node,
  type NodeTypes,
} from '@xyflow/react';

import '@xyflow/react/dist/style.css';

import { YouTubeNode } from './nodes/youtube-node';
import { VoiceRecorderNode } from './nodes/voice-recorder-node';
import { ImageNode } from './nodes/image-node';
import { ChatNode } from './nodes/chat-node';
import { FlowToolbar } from './flow-toolbar';

const nodeTypes: NodeTypes = {
  youtube: YouTubeNode,
  voiceRecorder: VoiceRecorderNode,
  image: ImageNode,
  chat: ChatNode,
};

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'chat',
    position: { x: 250, y: 250 },
    data: { label: 'Chat Interface' },
  },
];

const initialEdges: Edge[] = [];

interface FlowCanvasProps {
  boardId: string;
}

export function FlowCanvas({ boardId }: FlowCanvasProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const addNode = useCallback(
    (type: string) => {
      const newNode: Node = {
        id: `${Date.now()}`,
        type,
        position: {
          x: Math.random() * 400,
          y: Math.random() * 400,
        },
        data: { label: `${type} node` },
      };
      setNodes((nds) => [...nds, newNode]);
    },
    [setNodes]
  );

  return (
    <div className="w-full h-full relative">
      <FlowToolbar onAddNode={addNode} />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        className="bg-background"
      >
        <Background />
        <Controls />
        <MiniMap />
      </ReactFlow>
    </div>
  );
}
