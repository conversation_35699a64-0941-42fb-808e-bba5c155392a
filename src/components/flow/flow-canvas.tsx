'use client';

import {
  Background,
  type Connection,
  Controls,
  type Edge,
  MiniMap,
  type Node,
  type NodeTypes,
  ReactFlow,
  addEdge,
  useEdgesState,
  useNodesState,
} from '@xyflow/react';
import { useCallback } from 'react';

import '@xyflow/react/dist/style.css';
import './flow-theme.css';

import { FlowToolbar } from './flow-toolbar';
import { ChatNode } from './nodes/chat-node';
import { ImageNode } from './nodes/image-node';
import { VoiceRecorderNode } from './nodes/voice-recorder-node';
import { YouTubeNode } from './nodes/youtube-node';

const nodeTypes = {
  youtube: YouTubeNode,
  voiceRecorder: VoiceRecorderNode,
  image: ImageNode,
  chat: ChatNode,
} satisfies NodeTypes;

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'chat',
    position: { x: 250, y: 250 },
    data: { label: 'Chat Interface' },
  },
];

const initialEdges: Edge[] = [];

interface FlowCanvasProps {
  boardId: string;
}

export function FlowCanvas({ boardId: _boardId }: FlowCanvasProps) {
  // TODO: Use _boardId for saving/loading board state
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const addNode = useCallback(
    (type: string) => {
      const newNode: Node = {
        id: `${Date.now()}`,
        type,
        position: {
          x: Math.random() * 400,
          y: Math.random() * 400,
        },
        data: { label: `${type} node` },
      };
      setNodes((nds) => [...nds, newNode]);
    },
    [setNodes]
  );

  return (
    <div className="w-full h-full relative">
      <FlowToolbar onAddNode={addNode} />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        className="bg-background"
      >
        <Background color="hsl(var(--border))" gap={20} size={1} />
        <Controls className="!bg-background !border-border [&>button]:!bg-background [&>button]:!border-border [&>button]:!text-foreground [&>button:hover]:!bg-accent" />
        <MiniMap
          className="!bg-background !border-border"
          nodeColor={(node) => {
            switch (node.type) {
              case 'youtube':
                return 'hsl(var(--destructive))';
              case 'voiceRecorder':
                return 'hsl(var(--primary))';
              case 'image':
                return 'hsl(var(--secondary))';
              case 'chat':
                return 'hsl(var(--accent))';
              default:
                return 'hsl(var(--muted))';
            }
          }}
          maskColor="hsl(var(--background) / 0.8)"
        />
      </ReactFlow>
    </div>
  );
}
