'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MessageSquareIcon,
  MicIcon,
  ImageIcon,
  YoutubeIcon,
  PlusIcon,
} from 'lucide-react';

interface FlowToolbarProps {
  onAddNode: (type: string) => void;
}

export function FlowToolbar({ onAddNode }: FlowToolbarProps) {
  return (
    <div className="absolute top-4 left-4 z-10 flex gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Node
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem onClick={() => onAddNode('youtube')}>
            <YoutubeIcon className="h-4 w-4 mr-2" />
            YouTube Video
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onAddNode('voiceRecorder')}>
            <MicIcon className="h-4 w-4 mr-2" />
            Voice Recorder
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onAddNode('image')}>
            <ImageIcon className="h-4 w-4 mr-2" />
            Image
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onAddNode('chat')}>
            <MessageSquareIcon className="h-4 w-4 mr-2" />
            Chat Interface
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
