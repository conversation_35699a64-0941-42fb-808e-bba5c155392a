'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, type NodeProps, Position } from '@xyflow/react';
import { AnimatePresence, motion } from 'framer-motion';
import { ExternalLinkIcon, PlayIcon, YoutubeIcon } from 'lucide-react';
import { useCallback, useState } from 'react';

interface YouTubeData {
  url?: string;
  videoId?: string;
  title?: string;
  thumbnail?: string;
  duration?: string;
  label?: string;
}

export function YouTubeNode({ data }: NodeProps) {
  const nodeData = data as YouTubeData;
  const [url, setUrl] = useState(nodeData.url || '');
  const [videoData, setVideoData] = useState<YouTubeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const extractVideoId = (url: string): string | null => {
    const regex =
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const handleUrlSubmit = useCallback(async () => {
    if (!url) return;

    setIsLoading(true);
    const videoId = extractVideoId(url);

    if (videoId) {
      // In a real app, you'd fetch video metadata from YouTube API
      // For now, we'll use the video ID to generate thumbnail
      const thumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;

      setVideoData({
        url,
        videoId,
        title: 'YouTube Video', // Would be fetched from API
        thumbnail,
        duration: '0:00', // Would be fetched from API
      });
    }
    setIsLoading(false);
  }, [url]);

  const openInYouTube = () => {
    if (videoData?.url) {
      window.open(videoData.url, '_blank');
    }
  };

  return (
    <Card className="w-80 min-h-[200px]">
      <Handle type="target" position={Position.Top} />

      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <YoutubeIcon className="h-4 w-4 text-red-500" />
          YouTube Video
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="flex gap-2">
          <Input
            placeholder="Paste YouTube URL here..."
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="text-xs"
          />
          <Button
            size="sm"
            onClick={handleUrlSubmit}
            disabled={isLoading || !url}
          >
            Load
          </Button>
        </div>

        <AnimatePresence>
          {videoData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-2"
            >
              <div className="relative">
                <motion.img
                  src={videoData.thumbnail}
                  alt={videoData.title}
                  className="w-full h-32 object-cover rounded"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  onError={(e) => {
                    e.currentTarget.src = `https://img.youtube.com/vi/${videoData.videoId}/hqdefault.jpg`;
                  }}
                />
                <motion.div
                  className="absolute inset-0 flex items-center justify-center"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-black/50 hover:bg-black/70"
                      onClick={openInYouTube}
                    >
                      <PlayIcon className="h-4 w-4" />
                    </Button>
                  </motion.div>
                </motion.div>
              </div>

              <motion.div
                className="flex items-center justify-between"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <span className="text-xs font-medium truncate">
                  {videoData.title}
                </span>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Button size="sm" variant="ghost" onClick={openInYouTube}>
                    <ExternalLinkIcon className="h-3 w-3" />
                  </Button>
                </motion.div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>

      <Handle type="source" position={Position.Bottom} />
    </Card>
  );
}
