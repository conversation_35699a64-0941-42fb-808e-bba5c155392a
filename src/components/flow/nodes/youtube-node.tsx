'use client';

import { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { YoutubeIcon, PlayIcon, ExternalLinkIcon } from 'lucide-react';

interface YouTubeData {
  url?: string;
  videoId?: string;
  title?: string;
  thumbnail?: string;
  duration?: string;
}

export function YouTubeNode({ data }: NodeProps<YouTubeData>) {
  const [url, setUrl] = useState(data.url || '');
  const [videoData, setVideoData] = useState<YouTubeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const extractVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
  };

  const handleUrlSubmit = useCallback(async () => {
    if (!url) return;

    setIsLoading(true);
    const videoId = extractVideoId(url);
    
    if (videoId) {
      // In a real app, you'd fetch video metadata from YouTube API
      // For now, we'll use the video ID to generate thumbnail
      const thumbnail = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
      
      setVideoData({
        url,
        videoId,
        title: 'YouTube Video', // Would be fetched from API
        thumbnail,
        duration: '0:00', // Would be fetched from API
      });
    }
    setIsLoading(false);
  }, [url]);

  const openInYouTube = () => {
    if (videoData?.url) {
      window.open(videoData.url, '_blank');
    }
  };

  return (
    <Card className="w-80 min-h-[200px]">
      <Handle type="target" position={Position.Top} />
      
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <YoutubeIcon className="h-4 w-4 text-red-500" />
          YouTube Video
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex gap-2">
          <Input
            placeholder="Paste YouTube URL here..."
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            className="text-xs"
          />
          <Button 
            size="sm" 
            onClick={handleUrlSubmit}
            disabled={isLoading || !url}
          >
            Load
          </Button>
        </div>

        {videoData && (
          <div className="space-y-2">
            <div className="relative">
              <img
                src={videoData.thumbnail}
                alt={videoData.title}
                className="w-full h-32 object-cover rounded"
                onError={(e) => {
                  e.currentTarget.src = `https://img.youtube.com/vi/${videoData.videoId}/hqdefault.jpg`;
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-black/50 hover:bg-black/70"
                  onClick={openInYouTube}
                >
                  <PlayIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium truncate">
                {videoData.title}
              </span>
              <Button
                size="sm"
                variant="ghost"
                onClick={openInYouTube}
              >
                <ExternalLinkIcon className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
      
      <Handle type="source" position={Position.Bottom} />
    </Card>
  );
}
