'use client';

import { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ImageIcon,
  UploadIcon,
  TrashIcon,
  ZoomInIcon,
  DownloadIcon,
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';

interface ImageData {
  imageUrl?: string;
  imageName?: string;
  imageSize?: number;
}

export function ImageNode({ data }: NodeProps<ImageData>) {
  const [imageUrl, setImageUrl] = useState<string | null>(data.imageUrl || null);
  const [imageName, setImageName] = useState<string>(data.imageName || '');
  const [imageSize, setImageSize] = useState<number>(data.imageSize || 0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setImageUrl(url);
      setImageName(file.name);
      setImageSize(file.size);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setImageUrl(url);
      setImageName(file.name);
      setImageSize(file.size);
    }
  }, []);

  const deleteImage = useCallback(() => {
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl);
    }
    setImageUrl(null);
    setImageName('');
    setImageSize(0);
  }, [imageUrl]);

  const downloadImage = useCallback(() => {
    if (imageUrl && imageName) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = imageName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [imageUrl, imageName]);

  const openImageInNewTab = useCallback(() => {
    if (imageUrl) {
      window.open(imageUrl, '_blank');
    }
  }, [imageUrl]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-80 min-h-[250px]">
      <Handle type="target" position={Position.Top} />
      
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <ImageIcon className="h-4 w-4" />
          Image
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {!imageUrl ? (
          <div className="space-y-3">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
            >
              <input {...getInputProps()} />
              <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                {isDragActive
                  ? 'Drop the image here...'
                  : 'Drag & drop an image here, or click to select'}
              </p>
            </div>
            
            <div className="text-center">
              <label htmlFor="image-upload" className="cursor-pointer">
                <Button variant="outline" size="sm" asChild>
                  <span>
                    <UploadIcon className="h-4 w-4 mr-2" />
                    Upload Image
                  </span>
                </Button>
              </label>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="relative">
              <img
                src={imageUrl}
                alt={imageName}
                className="w-full h-40 object-cover rounded"
              />
              <div className="absolute top-2 right-2 flex gap-1">
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={openImageInNewTab}
                  className="h-6 w-6 p-0"
                >
                  <ZoomInIcon className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={downloadImage}
                  className="h-6 w-6 p-0"
                >
                  <DownloadIcon className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={deleteImage}
                  className="h-6 w-6 p-0"
                >
                  <TrashIcon className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-xs font-medium truncate" title={imageName}>
                {imageName}
              </div>
              <div className="text-xs text-muted-foreground">
                {formatFileSize(imageSize)}
              </div>
            </div>
          </div>
        )}
      </CardContent>
      
      <Handle type="source" position={Position.Bottom} />
    </Card>
  );
}
