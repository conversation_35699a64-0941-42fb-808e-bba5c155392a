/* React Flow Dark Theme Overrides */

/* Controls styling */
.react-flow__controls {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.react-flow__controls-button {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: calc(var(--radius) - 4px) !important;
  transition: all 0.2s ease-in-out !important;
}

.react-flow__controls-button:hover {
  background: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
  border-color: hsl(var(--accent)) !important;
}

.react-flow__controls-button:disabled {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border-color: hsl(var(--border)) !important;
  opacity: 0.5 !important;
}

/* MiniMap styling */
.react-flow__minimap {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.react-flow__minimap-mask {
  fill: hsl(var(--background) / 0.8) !important;
}

.react-flow__minimap-node {
  fill: hsl(var(--muted)) !important;
  stroke: hsl(var(--border)) !important;
  stroke-width: 1px !important;
}

/* Background styling */
.react-flow__background {
  background: hsl(var(--background)) !important;
}

/* Edge styling */
.react-flow__edge-path {
  stroke: hsl(var(--border)) !important;
  stroke-width: 2px !important;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: hsl(var(--primary)) !important;
  stroke-width: 3px !important;
}

.react-flow__connectionline {
  stroke: hsl(var(--primary)) !important;
  stroke-width: 2px !important;
  stroke-dasharray: 5,5 !important;
}

/* Handle styling */
.react-flow__handle {
  background: hsl(var(--background)) !important;
  border: 2px solid hsl(var(--primary)) !important;
  width: 8px !important;
  height: 8px !important;
}

.react-flow__handle:hover {
  background: hsl(var(--primary)) !important;
  transform: scale(1.2) !important;
  transition: all 0.2s ease-in-out !important;
}

.react-flow__handle-connecting {
  background: hsl(var(--primary)) !important;
  transform: scale(1.3) !important;
}

/* Selection box styling */
.react-flow__selection {
  background: hsl(var(--primary) / 0.1) !important;
  border: 1px solid hsl(var(--primary)) !important;
}

/* Node selection styling */
.react-flow__node.selected {
  box-shadow: 0 0 0 2px hsl(var(--primary)) !important;
}

/* Attribution styling */
.react-flow__attribution {
  background: hsl(var(--background)) !important;
  color: hsl(var(--muted-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  font-size: 10px !important;
  padding: 2px 4px !important;
}

.react-flow__attribution a {
  color: hsl(var(--primary)) !important;
  text-decoration: none !important;
}

.react-flow__attribution a:hover {
  text-decoration: underline !important;
}

/* Panel styling for any additional panels */
.react-flow__panel {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: calc(var(--radius) - 2px) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

/* Viewport styling */
.react-flow__viewport {
  background: hsl(var(--background)) !important;
}

/* Pane styling */
.react-flow__pane {
  cursor: default !important;
}

.react-flow__pane.selection {
  cursor: crosshair !important;
}

/* Transform styling */
.react-flow__transform {
  background: hsl(var(--background)) !important;
}
